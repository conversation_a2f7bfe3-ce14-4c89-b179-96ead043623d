<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= title %> - Telegram卡密销售系统</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <style>
        .sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.8);
            border-radius: 8px;
            margin: 2px 0;
            transition: all 0.3s;
        }
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            color: white;
            background-color: rgba(255, 255, 255, 0.1);
        }
        .card {
            border: none;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .table th {
            border-top: none;
            background-color: #f8f9fa;
            font-weight: 600;
        }
        .badge-status {
            font-size: 0.75rem;
            padding: 0.375rem 0.75rem;
        }
        .product-image {
            width: 50px;
            height: 50px;
            object-fit: cover;
            border-radius: 8px;
        }
    </style>
</head>
<body class="bg-light">
    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="position-sticky pt-3">
                    <div class="text-center mb-4">
                        <h5 class="text-white">
                            <i class="fas fa-store me-2"></i>
                            卡密销售系统
                        </h5>
                        <small class="text-white-50">欢迎，<%= admin.username %></small>
                    </div>
                    
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link <%= currentPage === 'dashboard' ? 'active' : '' %>" href="/admin/dashboard">
                                <i class="fas fa-tachometer-alt me-2"></i>
                                仪表板
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <%= currentPage === 'products' ? 'active' : '' %>" href="/admin/products">
                                <i class="fas fa-box me-2"></i>
                                商品管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <%= currentPage === 'orders' ? 'active' : '' %>" href="/admin/orders">
                                <i class="fas fa-shopping-cart me-2"></i>
                                订单管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <%= currentPage === 'cards' ? 'active' : '' %>" href="/admin/cards">
                                <i class="fas fa-credit-card me-2"></i>
                                卡密管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <%= currentPage === 'users' ? 'active' : '' %>" href="/admin/users">
                                <i class="fas fa-users me-2"></i>
                                用户管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <%= currentPage === 'reports' ? 'active' : '' %>" href="/admin/reports">
                                <i class="fas fa-chart-bar me-2"></i>
                                统计报表
                            </a>
                        </li>
                        <li class="nav-item mt-3">
                            <a class="nav-link" href="/admin/logout">
                                <i class="fas fa-sign-out-alt me-2"></i>
                                退出登录
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- 主内容区 -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">
                        <i class="fas fa-box me-2"></i>
                        商品管理
                    </h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <button type="button" class="btn btn-sm btn-primary" data-bs-toggle="modal" data-bs-target="#addProductModal">
                                <i class="fas fa-plus me-1"></i>
                                添加商品
                            </button>
                            <button type="button" class="btn btn-sm btn-outline-secondary">
                                <i class="fas fa-download me-1"></i>
                                导出数据
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 商品列表 -->
                <div class="card">
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>商品名称</th>
                                        <th>分类</th>
                                        <th>价格</th>
                                        <th>库存</th>
                                        <th>已售</th>
                                        <th>状态</th>
                                        <th>创建时间</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <% if (products && products.length > 0) { %>
                                        <% products.forEach(product => { %>
                                            <tr>
                                                <td><%= product.id %></td>
                                                <td>
                                                    <div class="d-flex align-items-center">
                                                        <% if (product.image_url) { %>
                                                            <img src="<%= product.image_url %>" alt="<%= product.name %>" class="product-image me-3">
                                                        <% } else { %>
                                                            <div class="bg-secondary product-image me-3 d-flex align-items-center justify-content-center">
                                                                <i class="fas fa-image text-white"></i>
                                                            </div>
                                                        <% } %>
                                                        <div>
                                                            <h6 class="mb-0"><%= product.name %></h6>
                                                            <% if (product.description) { %>
                                                                <small class="text-muted"><%= product.description.substring(0, 50) %>...</small>
                                                            <% } %>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td><%= product.category_name || '未分类' %></td>
                                                <td>
                                                    <strong>¥<%= parseFloat(product.price).toFixed(2) %></strong>
                                                    <% if (product.original_price && product.original_price > product.price) { %>
                                                        <br><small class="text-muted text-decoration-line-through">¥<%= parseFloat(product.original_price).toFixed(2) %></small>
                                                    <% } %>
                                                </td>
                                                <td>
                                                    <span class="badge <%= product.available_cards > product.min_stock_alert ? 'bg-success' : 'bg-warning' %>">
                                                        <%= product.available_cards %>
                                                    </span>
                                                </td>
                                                <td><%= product.sold_count %></td>
                                                <td>
                                                    <% if (product.status === 'active') { %>
                                                        <span class="badge bg-success badge-status">上架</span>
                                                    <% } else if (product.status === 'inactive') { %>
                                                        <span class="badge bg-secondary badge-status">下架</span>
                                                    <% } else { %>
                                                        <span class="badge bg-danger badge-status">缺货</span>
                                                    <% } %>
                                                </td>
                                                <td><%= new Date(product.created_at).toLocaleDateString('zh-CN') %></td>
                                                <td>
                                                    <div class="btn-group btn-group-sm">
                                                        <button type="button" class="btn btn-outline-primary" title="编辑">
                                                            <i class="fas fa-edit"></i>
                                                        </button>
                                                        <button type="button" class="btn btn-outline-info" title="查看卡密">
                                                            <i class="fas fa-credit-card"></i>
                                                        </button>
                                                        <button type="button" class="btn btn-outline-danger" title="删除">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                        <% }); %>
                                    <% } else { %>
                                        <tr>
                                            <td colspan="9" class="text-center text-muted py-4">
                                                <i class="fas fa-box-open fa-3x mb-3 opacity-25"></i>
                                                <p>暂无商品数据</p>
                                            </td>
                                        </tr>
                                    <% } %>
                                </tbody>
                            </table>
                        </div>

                        <!-- 分页 -->
                        <% if (pagination && pagination.totalPages > 1) { %>
                            <nav aria-label="商品分页">
                                <ul class="pagination justify-content-center">
                                    <li class="page-item <%= !pagination.hasPrev ? 'disabled' : '' %>">
                                        <a class="page-link" href="?page=<%= pagination.page - 1 %>">上一页</a>
                                    </li>
                                    
                                    <% for (let i = Math.max(1, pagination.page - 2); i <= Math.min(pagination.totalPages, pagination.page + 2); i++) { %>
                                        <li class="page-item <%= i === pagination.page ? 'active' : '' %>">
                                            <a class="page-link" href="?page=<%= i %>"><%= i %></a>
                                        </li>
                                    <% } %>
                                    
                                    <li class="page-item <%= !pagination.hasNext ? 'disabled' : '' %>">
                                        <a class="page-link" href="?page=<%= pagination.page + 1 %>">下一页</a>
                                    </li>
                                </ul>
                            </nav>
                        <% } %>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- 添加商品模态框 -->
    <div class="modal fade" id="addProductModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-plus me-2"></i>
                        添加商品
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="addProductForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="productName" class="form-label">商品名称 *</label>
                                    <input type="text" class="form-control" id="productName" name="name" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="productCategory" class="form-label">商品分类</label>
                                    <select class="form-select" id="productCategory" name="category_id">
                                        <option value="">选择分类</option>
                                        <option value="1">游戏充值</option>
                                        <option value="2">软件激活</option>
                                        <option value="3">会员服务</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="productDescription" class="form-label">商品描述</label>
                            <textarea class="form-control" id="productDescription" name="description" rows="3"></textarea>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="productPrice" class="form-label">销售价格 *</label>
                                    <div class="input-group">
                                        <span class="input-group-text">¥</span>
                                        <input type="number" class="form-control" id="productPrice" name="price" step="0.01" required>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="productOriginalPrice" class="form-label">原价</label>
                                    <div class="input-group">
                                        <span class="input-group-text">¥</span>
                                        <input type="number" class="form-control" id="productOriginalPrice" name="original_price" step="0.01">
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="productMinStock" class="form-label">库存预警</label>
                                    <input type="number" class="form-control" id="productMinStock" name="min_stock_alert" value="10">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="productSortOrder" class="form-label">排序</label>
                                    <input type="number" class="form-control" id="productSortOrder" name="sort_order" value="0">
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="productImage" class="form-label">商品图片</label>
                            <input type="url" class="form-control" id="productImage" name="image_url" placeholder="图片URL">
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="submitProduct()">
                        <i class="fas fa-save me-1"></i>
                        保存
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        function submitProduct() {
            const form = document.getElementById('addProductForm');
            const formData = new FormData(form);
            const data = Object.fromEntries(formData);
            
            fetch('/api/products', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(result => {
                if (result.success) {
                    location.reload();
                } else {
                    alert('添加失败: ' + result.message);
                }
            })
            .catch(error => {
                alert('添加失败: ' + error.message);
            });
        }
    </script>
</body>
</html>
