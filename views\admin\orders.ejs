<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= title %> - Telegram卡密销售系统</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <style>
        .sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.8);
            border-radius: 8px;
            margin: 2px 0;
            transition: all 0.3s;
        }
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            color: white;
            background-color: rgba(255, 255, 255, 0.1);
        }
        .card {
            border: none;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .table th {
            border-top: none;
            background-color: #f8f9fa;
            font-weight: 600;
        }
        .badge-status {
            font-size: 0.75rem;
            padding: 0.375rem 0.75rem;
        }
    </style>
</head>
<body class="bg-light">
    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="position-sticky pt-3">
                    <div class="text-center mb-4">
                        <h5 class="text-white">
                            <i class="fas fa-store me-2"></i>
                            卡密销售系统
                        </h5>
                        <small class="text-white-50">欢迎，<%= admin.username %></small>
                    </div>
                    
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link <%= currentPage === 'dashboard' ? 'active' : '' %>" href="/admin/dashboard">
                                <i class="fas fa-tachometer-alt me-2"></i>
                                仪表板
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <%= currentPage === 'products' ? 'active' : '' %>" href="/admin/products">
                                <i class="fas fa-box me-2"></i>
                                商品管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <%= currentPage === 'orders' ? 'active' : '' %>" href="/admin/orders">
                                <i class="fas fa-shopping-cart me-2"></i>
                                订单管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <%= currentPage === 'cards' ? 'active' : '' %>" href="/admin/cards">
                                <i class="fas fa-credit-card me-2"></i>
                                卡密管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <%= currentPage === 'users' ? 'active' : '' %>" href="/admin/users">
                                <i class="fas fa-users me-2"></i>
                                用户管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <%= currentPage === 'reports' ? 'active' : '' %>" href="/admin/reports">
                                <i class="fas fa-chart-bar me-2"></i>
                                统计报表
                            </a>
                        </li>
                        <li class="nav-item mt-3">
                            <a class="nav-link" href="/admin/logout">
                                <i class="fas fa-sign-out-alt me-2"></i>
                                退出登录
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- 主内容区 -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">
                        <i class="fas fa-shopping-cart me-2"></i>
                        订单管理
                    </h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <button type="button" class="btn btn-sm btn-outline-secondary">
                                <i class="fas fa-download me-1"></i>
                                导出订单
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 状态筛选 -->
                <div class="card mb-4">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-md-6">
                                <h6 class="mb-0">订单筛选</h6>
                            </div>
                            <div class="col-md-6">
                                <div class="btn-group" role="group">
                                    <a href="/admin/orders" class="btn <%= !currentStatus ? 'btn-primary' : 'btn-outline-primary' %> btn-sm">
                                        全部
                                    </a>
                                    <a href="/admin/orders?status=pending" class="btn <%= currentStatus === 'pending' ? 'btn-warning' : 'btn-outline-warning' %> btn-sm">
                                        待支付
                                    </a>
                                    <a href="/admin/orders?status=completed" class="btn <%= currentStatus === 'completed' ? 'btn-success' : 'btn-outline-success' %> btn-sm">
                                        已完成
                                    </a>
                                    <a href="/admin/orders?status=cancelled" class="btn <%= currentStatus === 'cancelled' ? 'btn-danger' : 'btn-outline-danger' %> btn-sm">
                                        已取消
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 订单列表 -->
                <div class="card">
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>订单号</th>
                                        <th>用户</th>
                                        <th>商品</th>
                                        <th>数量</th>
                                        <th>金额</th>
                                        <th>支付方式</th>
                                        <th>状态</th>
                                        <th>创建时间</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <% if (orders && orders.length > 0) { %>
                                        <% orders.forEach(order => { %>
                                            <tr>
                                                <td>
                                                    <code><%= order.order_no %></code>
                                                </td>
                                                <td>
                                                    <% if (order.username) { %>
                                                        <div>@<%= order.username %></div>
                                                        <small class="text-muted">ID: <%= order.telegram_id %></small>
                                                    <% } else { %>
                                                        <div>用户<%= order.telegram_id %></div>
                                                    <% } %>
                                                </td>
                                                <td>
                                                    <div><%= order.product_name %></div>
                                                    <small class="text-muted">ID: <%= order.product_id %></small>
                                                </td>
                                                <td><%= order.quantity %>张</td>
                                                <td>
                                                    <strong>¥<%= parseFloat(order.total_amount).toFixed(2) %></strong>
                                                    <% if (order.unit_price !== order.total_amount) { %>
                                                        <br><small class="text-muted">单价: ¥<%= parseFloat(order.unit_price).toFixed(2) %></small>
                                                    <% } %>
                                                </td>
                                                <td>
                                                    <% if (order.payment_method === 'usdt') { %>
                                                        <span class="badge bg-info">USDT</span>
                                                    <% } else if (order.payment_method === 'alipay') { %>
                                                        <span class="badge bg-primary">支付宝</span>
                                                    <% } else { %>
                                                        <span class="badge bg-secondary"><%= order.payment_method %></span>
                                                    <% } %>
                                                </td>
                                                <td>
                                                    <% if (order.status === 'completed') { %>
                                                        <span class="badge bg-success badge-status">已完成</span>
                                                    <% } else if (order.status === 'pending') { %>
                                                        <span class="badge bg-warning badge-status">待支付</span>
                                                    <% } else if (order.status === 'paid') { %>
                                                        <span class="badge bg-info badge-status">已支付</span>
                                                    <% } else if (order.status === 'cancelled') { %>
                                                        <span class="badge bg-danger badge-status">已取消</span>
                                                    <% } else if (order.status === 'expired') { %>
                                                        <span class="badge bg-secondary badge-status">已过期</span>
                                                    <% } else { %>
                                                        <span class="badge bg-secondary badge-status"><%= order.status %></span>
                                                    <% } %>
                                                </td>
                                                <td>
                                                    <div><%= new Date(order.created_at).toLocaleDateString('zh-CN') %></div>
                                                    <small class="text-muted"><%= new Date(order.created_at).toLocaleTimeString('zh-CN') %></small>
                                                </td>
                                                <td>
                                                    <div class="btn-group btn-group-sm">
                                                        <button type="button" class="btn btn-outline-primary" title="查看详情" onclick="viewOrder('<%= order.id %>')">
                                                            <i class="fas fa-eye"></i>
                                                        </button>
                                                        <% if (order.status === 'pending') { %>
                                                            <button type="button" class="btn btn-outline-success" title="确认支付" onclick="confirmPayment('<%= order.id %>')">
                                                                <i class="fas fa-check"></i>
                                                            </button>
                                                        <% } %>
                                                        <% if (order.status === 'pending' || order.status === 'paid') { %>
                                                            <button type="button" class="btn btn-outline-danger" title="取消订单" onclick="cancelOrder('<%= order.id %>')">
                                                                <i class="fas fa-times"></i>
                                                            </button>
                                                        <% } %>
                                                    </div>
                                                </td>
                                            </tr>
                                        <% }); %>
                                    <% } else { %>
                                        <tr>
                                            <td colspan="9" class="text-center text-muted py-4">
                                                <i class="fas fa-shopping-cart fa-3x mb-3 opacity-25"></i>
                                                <p>暂无订单数据</p>
                                            </td>
                                        </tr>
                                    <% } %>
                                </tbody>
                            </table>
                        </div>

                        <!-- 分页 -->
                        <% if (pagination && pagination.totalPages > 1) { %>
                            <nav aria-label="订单分页">
                                <ul class="pagination justify-content-center">
                                    <li class="page-item <%= !pagination.hasPrev ? 'disabled' : '' %>">
                                        <a class="page-link" href="?page=<%= pagination.page - 1 %><%= currentStatus ? '&status=' + currentStatus : '' %>">上一页</a>
                                    </li>
                                    
                                    <% for (let i = Math.max(1, pagination.page - 2); i <= Math.min(pagination.totalPages, pagination.page + 2); i++) { %>
                                        <li class="page-item <%= i === pagination.page ? 'active' : '' %>">
                                            <a class="page-link" href="?page=<%= i %><%= currentStatus ? '&status=' + currentStatus : '' %>"><%= i %></a>
                                        </li>
                                    <% } %>
                                    
                                    <li class="page-item <%= !pagination.hasNext ? 'disabled' : '' %>">
                                        <a class="page-link" href="?page=<%= pagination.page + 1 %><%= currentStatus ? '&status=' + currentStatus : '' %>">下一页</a>
                                    </li>
                                </ul>
                            </nav>
                        <% } %>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        function viewOrder(orderId) {
            // 查看订单详情
            window.open(`/admin/orders/${orderId}`, '_blank');
        }
        
        function confirmPayment(orderId) {
            if (confirm('确认手动确认此订单的支付状态？')) {
                fetch('/api/payments/confirm', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ order_id: orderId })
                })
                .then(response => response.json())
                .then(result => {
                    if (result.success) {
                        alert('支付确认成功');
                        location.reload();
                    } else {
                        alert('确认失败: ' + result.message);
                    }
                })
                .catch(error => {
                    alert('确认失败: ' + error.message);
                });
            }
        }
        
        function cancelOrder(orderId) {
            if (confirm('确认取消此订单？')) {
                // 这里可以调用取消订单的API
                alert('取消订单功能开发中...');
            }
        }
    </script>
</body>
</html>
